#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式登录脚本 - 允许手动处理验证码
"""
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import pickle
import ssl
import urllib3
import json

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

def interactive_login():
    """交互式登录到大麦网"""
    print("🎫 大麦网交互式登录")
    print("=" * 50)
    
    # 加载配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        username = config.get('username', '')
        password = config.get('password', '')
        target_url = config.get('target_url', '')
    except:
        print("❌ 无法加载配置文件")
        return
    
    if not username or not password:
        print("❌ 用户名或密码未配置")
        return
    
    # 启动浏览器
    try:
        driver = uc.Chrome()
        wait = WebDriverWait(driver, 15)
        print("✅ Chrome浏览器启动成功")
    except Exception as e:
        print(f"❌ Chrome启动失败: {e}")
        return
    
    try:
        # 访问大麦网
        print("📱 正在访问大麦网...")
        driver.get("https://www.damai.cn/")
        time.sleep(5)
        print("✅ 大麦网首页加载完成")
        
        # 尝试加载已保存的cookies
        try:
            with open('cookies.pkl', 'rb') as f:
                cookies = pickle.load(f)
                for cookie in cookies:
                    driver.add_cookie(cookie)
            driver.refresh()
            time.sleep(3)
            print("✅ 已加载保存的cookies")
        except:
            print("⚠️  没有找到保存的cookies，需要重新登录")
        
        # 检查是否已经登录
        try:
            driver.find_element(By.XPATH, "//div[@class='user-info']")
            print("🎉 已经登录成功！")
            save_cookies(driver)
            
            # 继续抢票流程
            if target_url:
                print(f"🎯 正在访问目标页面: {target_url}")
                driver.get(target_url)
                time.sleep(5)
                print("✅ 目标页面加载完成")
                
                input("请手动选择票档和数量，然后按回车继续抢票...")
                
                # 尝试点击购买按钮
                buy_selectors = [
                    "//div[contains(@class, 'buy-btn')]",
                    "//button[contains(text(), '立即购买')]",
                    "//button[contains(text(), '立即预订')]",
                    "//a[contains(text(), '立即购买')]"
                ]
                
                for selector in buy_selectors:
                    try:
                        buy_button = driver.find_element(By.XPATH, selector)
                        if buy_button.is_displayed() and buy_button.is_enabled():
                            print(f"🖱️  找到购买按钮: {selector}")
                            buy_button.click()
                            print("✅ 已点击购买按钮")
                            break
                    except:
                        continue
                
                input("请手动完成后续购买流程，完成后按回车关闭浏览器...")
            else:
                input("登录成功！请手动进行抢票操作，完成后按回车关闭浏览器...")
            
            return
            
        except:
            print("🔐 需要登录，开始登录流程...")
        
        # 查找并点击登录按钮
        login_selectors = [
            "//span[text()='登录']",
            "//span[contains(text(), '登录')]",
            "//a[text()='登录']",
            "//a[contains(text(), '登录')]"
        ]
        
        login_button = None
        for selector in login_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        login_button = element
                        break
                if login_button:
                    break
            except:
                continue
        
        if login_button:
            print("🖱️  点击登录按钮...")
            login_button.click()
            time.sleep(3)
        else:
            print("❌ 找不到登录按钮")
            return
        
        # 切换到登录iframe
        try:
            iframe_selectors = [
                "//iframe[contains(@src, 'login')]",
                "//iframe[contains(@src, 'passport')]"
            ]
            
            iframe = None
            for selector in iframe_selectors:
                try:
                    iframe = wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    break
                except:
                    continue
            
            if iframe:
                driver.switch_to.frame(iframe)
                print("✅ 已切换到登录iframe")
            else:
                print("⚠️  未找到登录iframe，继续尝试...")
        except Exception as e:
            print(f"⚠️  切换iframe失败: {e}")
        
        # 尝试切换到密码登录
        try:
            time.sleep(2)
            password_login_button = driver.find_element(By.XPATH, "//a[text()='密码登录']")
            password_login_button.click()
            print("✅ 已切换到密码登录")
            time.sleep(1)
        except:
            print("⚠️  未找到密码登录按钮，可能已经是密码登录模式")
        
        # 输入用户名和密码
        try:
            time.sleep(2)
            username_input = driver.find_element(By.ID, "fm-login-id")
            password_input = driver.find_element(By.ID, "fm-login-password")
            
            username_input.clear()
            password_input.clear()
            
            # 模拟人类输入
            for char in username:
                username_input.send_keys(char)
                time.sleep(0.1)
            
            for char in password:
                password_input.send_keys(char)
                time.sleep(0.1)
            
            print("✅ 已输入用户名和密码")
        except Exception as e:
            print(f"❌ 输入用户名密码失败: {e}")
            return
        
        # 提示用户处理验证码
        print("\n⚠️  请手动处理验证码（如果有的话），然后点击登录按钮")
        input("处理完验证码并点击登录后，按回车继续...")
        
        # 等待登录完成
        print("⏳ 等待登录完成...")
        time.sleep(5)
        
        # 切换回主页面
        try:
            driver.switch_to.default_content()
        except:
            pass
        
        # 检查登录状态
        try:
            driver.find_element(By.XPATH, "//div[@class='user-info']")
            print("🎉 登录成功！")
            save_cookies(driver)
            
            # 如果有目标URL，继续抢票
            if target_url:
                print(f"🎯 正在访问目标页面: {target_url}")
                driver.get(target_url)
                time.sleep(5)
                print("✅ 目标页面加载完成")
                
                input("请手动选择票档和数量，然后按回车继续...")
                
                # 尝试自动点击购买按钮
                buy_selectors = [
                    "//div[contains(@class, 'buy-btn')]",
                    "//button[contains(text(), '立即购买')]",
                    "//button[contains(text(), '立即预订')]"
                ]
                
                for selector in buy_selectors:
                    try:
                        buy_button = driver.find_element(By.XPATH, selector)
                        if buy_button.is_displayed() and buy_button.is_enabled():
                            print(f"🖱️  点击购买按钮: {selector}")
                            buy_button.click()
                            print("✅ 已点击购买按钮")
                            break
                    except:
                        continue
                
                input("请手动完成后续购买流程，完成后按回车关闭浏览器...")
            else:
                input("登录成功！请手动进行抢票操作，完成后按回车关闭浏览器...")
                
        except:
            print("❌ 登录可能失败，请检查")
            input("按回车关闭浏览器...")
        
    except Exception as e:
        print(f"❌ 过程中出错: {e}")
    finally:
        try:
            driver.quit()
            print("🔚 浏览器已关闭")
        except:
            pass

def save_cookies(driver):
    """保存cookies"""
    try:
        with open('cookies.pkl', 'wb') as f:
            pickle.dump(driver.get_cookies(), f)
        print("💾 Cookies已保存")
    except Exception as e:
        print(f"⚠️  保存cookies失败: {e}")

if __name__ == '__main__':
    interactive_login()
