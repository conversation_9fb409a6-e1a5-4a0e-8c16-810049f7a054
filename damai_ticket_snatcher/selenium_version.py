#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用标准selenium的版本
"""
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import time
import pickle
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def selenium_login():
    """使用标准selenium进行登录"""
    print("🎫 大麦网登录 (标准Selenium版本)")
    print("=" * 50)
    
    # 加载配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        username = config.get('username', '')
        password = config.get('password', '')
        target_url = config.get('target_url', '')
    except:
        print("❌ 无法加载配置文件")
        return
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    # 启动Chrome
    try:
        print("📱 正在启动Chrome...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        wait = WebDriverWait(driver, 15)
        print("✅ Chrome启动成功")
    except Exception as e:
        print(f"❌ Chrome启动失败: {e}")
        return
    
    try:
        # 访问大麦网
        print("🌐 正在访问大麦网...")
        driver.get("https://www.damai.cn/")
        time.sleep(5)
        print("✅ 大麦网首页加载完成")
        print(f"📄 页面标题: {driver.title}")
        
        # 尝试加载cookies
        try:
            with open('cookies.pkl', 'rb') as f:
                cookies = pickle.load(f)
                for cookie in cookies:
                    driver.add_cookie(cookie)
            driver.refresh()
            time.sleep(3)
            print("✅ 已加载保存的cookies")
        except:
            print("⚠️  没有找到保存的cookies")
        
        # 检查登录状态
        try:
            user_info = driver.find_element(By.XPATH, "//div[@class='user-info']")
            print("🎉 已经登录成功！")
            
            # 继续抢票
            if target_url:
                start_ticket_snatching(driver, target_url)
            else:
                input("登录成功！请手动进行抢票，完成后按回车...")
            return
        except:
            print("🔐 需要登录...")
        
        # 查找登录按钮
        login_selectors = [
            "//span[text()='登录']",
            "//span[contains(text(), '登录')]",
            "//a[text()='登录']",
            "//a[contains(text(), '登录')]"
        ]
        
        login_button = None
        for selector in login_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        login_button = element
                        print(f"✅ 找到登录按钮: {selector}")
                        break
                if login_button:
                    break
            except:
                continue
        
        if not login_button:
            print("❌ 找不到登录按钮")
            input("请手动点击登录按钮，然后按回车...")
        else:
            login_button.click()
            time.sleep(3)
            print("✅ 已点击登录按钮")
        
        # 处理登录iframe
        try:
            iframe = wait.until(EC.presence_of_element_located((By.XPATH, "//iframe[contains(@src, 'login')]")))
            driver.switch_to.frame(iframe)
            print("✅ 已切换到登录iframe")
        except:
            print("⚠️  未找到登录iframe")
        
        # 切换到密码登录
        try:
            password_login = driver.find_element(By.XPATH, "//a[text()='密码登录']")
            password_login.click()
            time.sleep(1)
            print("✅ 已切换到密码登录")
        except:
            print("⚠️  未找到密码登录按钮")
        
        # 输入用户名密码
        try:
            username_input = driver.find_element(By.ID, "fm-login-id")
            password_input = driver.find_element(By.ID, "fm-login-password")
            
            username_input.clear()
            password_input.clear()
            
            username_input.send_keys(username)
            password_input.send_keys(password)
            print("✅ 已输入用户名和密码")
        except Exception as e:
            print(f"❌ 输入失败: {e}")
        
        # 等待用户处理验证码
        print("\n⚠️  请手动处理验证码并点击登录按钮")
        input("完成登录后按回车继续...")
        
        # 切换回主页面
        driver.switch_to.default_content()
        time.sleep(3)
        
        # 检查登录状态
        try:
            driver.find_element(By.XPATH, "//div[@class='user-info']")
            print("🎉 登录成功！")
            
            # 保存cookies
            with open('cookies.pkl', 'wb') as f:
                pickle.dump(driver.get_cookies(), f)
            print("💾 Cookies已保存")
            
            # 开始抢票
            if target_url:
                start_ticket_snatching(driver, target_url)
            else:
                input("登录成功！请手动进行抢票，完成后按回车...")
                
        except:
            print("❌ 登录验证失败")
            input("请检查登录状态，然后按回车...")
        
    except Exception as e:
        print(f"❌ 过程中出错: {e}")
    finally:
        try:
            driver.quit()
            print("🔚 浏览器已关闭")
        except:
            pass

def start_ticket_snatching(driver, target_url):
    """开始抢票流程"""
    print(f"🎯 开始抢票: {target_url}")
    
    try:
        driver.get(target_url)
        time.sleep(5)
        print("✅ 目标页面加载完成")
        
        # 等待用户选择票档
        input("请手动选择票档和数量，然后按回车开始抢票...")
        
        # 查找购买按钮
        buy_selectors = [
            "//div[contains(@class, 'buy-btn')]",
            "//button[contains(text(), '立即购买')]",
            "//button[contains(text(), '立即预订')]",
            "//a[contains(text(), '立即购买')]",
            "//div[contains(text(), '立即购买')]"
        ]
        
        max_attempts = 50
        for attempt in range(max_attempts):
            print(f"🔄 抢票尝试 {attempt + 1}/{max_attempts}")
            
            for selector in buy_selectors:
                try:
                    buy_button = driver.find_element(By.XPATH, selector)
                    if buy_button.is_displayed() and buy_button.is_enabled():
                        buy_button.click()
                        print(f"✅ 成功点击购买按钮! (选择器: {selector})")
                        
                        # 等待页面跳转
                        time.sleep(2)
                        
                        # 检查是否成功进入购买页面
                        current_url = driver.current_url
                        if "confirm" in current_url or "order" in current_url or "checkout" in current_url:
                            print("🎉 成功进入购买页面！")
                            input("请手动完成后续购买流程...")
                            return
                        
                        break
                except:
                    continue
            
            # 刷新页面重试
            if attempt < max_attempts - 1:
                time.sleep(0.5)
                driver.refresh()
                time.sleep(2)
        
        print("❌ 抢票失败，已达到最大尝试次数")
        
    except Exception as e:
        print(f"❌ 抢票过程出错: {e}")

if __name__ == '__main__':
    selenium_login()
