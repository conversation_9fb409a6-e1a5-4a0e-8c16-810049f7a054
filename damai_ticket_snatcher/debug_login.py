#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试登录问题的简化脚本
"""
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import ssl
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

def debug_damai_login():
    """调试大麦网登录页面"""
    print("🔍 开始调试大麦网登录...")
    
    # 创建Chrome实例
    try:
        driver = uc.Chrome()
        wait = WebDriverWait(driver, 15)
        print("✅ Chrome浏览器启动成功")
    except Exception as e:
        print(f"❌ Chrome启动失败: {e}")
        return
    
    try:
        # 访问大麦网首页
        print("📱 正在访问大麦网首页...")
        driver.get("https://www.damai.cn/")
        time.sleep(5)  # 等待页面完全加载
        print("✅ 大麦网首页加载完成")
        
        # 获取页面标题
        print(f"📄 页面标题: {driver.title}")
        
        # 查找所有可能的登录相关元素
        print("\n🔍 搜索登录相关元素...")
        
        # 尝试多种登录按钮选择器
        login_selectors = [
            "//a[text()='登录']",
            "//a[contains(text(), '登录')]",
            "//span[text()='登录']",
            "//span[contains(text(), '登录')]",
            "//div[text()='登录']",
            "//div[contains(text(), '登录')]",
            "//button[text()='登录']",
            "//button[contains(text(), '登录')]",
            "//*[contains(@class, 'login')]",
            "//*[contains(@id, 'login')]"
        ]
        
        found_elements = []
        for i, selector in enumerate(login_selectors):
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    for j, element in enumerate(elements):
                        try:
                            text = element.text.strip()
                            is_displayed = element.is_displayed()
                            is_enabled = element.is_enabled()
                            print(f"  找到元素 {i+1}-{j+1}: '{text}' (显示:{is_displayed}, 可用:{is_enabled})")
                            if is_displayed and is_enabled:
                                found_elements.append((element, selector, text))
                        except:
                            pass
            except:
                pass
        
        if found_elements:
            print(f"\n✅ 找到 {len(found_elements)} 个可用的登录元素")
            
            # 尝试点击第一个找到的登录元素
            try:
                element, selector, text = found_elements[0]
                print(f"🖱️  尝试点击: '{text}'")
                element.click()
                time.sleep(3)
                print("✅ 点击成功，等待登录页面...")
                
                # 检查是否出现了登录表单
                login_form_selectors = [
                    "//iframe[contains(@src, 'login')]",
                    "//iframe[contains(@src, 'passport')]",
                    "//form[contains(@class, 'login')]",
                    "//div[contains(@class, 'login-form')]",
                    "//input[@type='password']"
                ]
                
                for selector in login_form_selectors:
                    try:
                        elements = driver.find_elements(By.XPATH, selector)
                        if elements:
                            print(f"✅ 找到登录表单元素: {selector}")
                    except:
                        pass
                        
            except Exception as e:
                print(f"❌ 点击登录按钮失败: {e}")
        else:
            print("❌ 未找到任何登录元素")
            
            # 打印页面源码的一部分来调试
            print("\n📝 页面源码片段:")
            page_source = driver.page_source
            if "登录" in page_source:
                print("✅ 页面源码中包含'登录'文字")
                # 找到包含登录的行
                lines = page_source.split('\n')
                for i, line in enumerate(lines):
                    if "登录" in line:
                        print(f"  行 {i}: {line.strip()[:100]}...")
                        if i < 5:  # 只显示前5个匹配行
                            break
            else:
                print("❌ 页面源码中不包含'登录'文字")
        
        # 等待用户观察
        print("\n⏳ 浏览器将保持打开30秒供您观察...")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
    finally:
        try:
            driver.quit()
            print("🔚 浏览器已关闭")
        except:
            pass

if __name__ == '__main__':
    debug_damai_login()
