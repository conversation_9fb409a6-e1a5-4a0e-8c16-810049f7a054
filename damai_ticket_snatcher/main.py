#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
import time
import pickle
import logging
import json
import os
import random
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ticket_snatcher.log'),
        logging.StreamHandler()
    ]
)

# Damai ticket snatching script
class DamaiTicketSnatcher:
    def __init__(self, config_file='config.json'):
        """
        Initialize the ticket snatcher with configuration file
        """
        logging.info("Initializing the ticket snatcher.")
        self.config = self.load_config(config_file)
        self.username = self.config.get('username', '')
        self.password = self.config.get('password', '')
        self.target_url = self.config.get('target_url', '')
        self.ticket_count = self.config.get('ticket_count', 1)
        self.price_level = self.config.get('price_level', 0)  # 0: highest, 1: second highest, etc.
        self.max_retry = self.config.get('max_retry', 10)
        self.retry_interval = self.config.get('retry_interval', 1)

        # Setup Chrome options for better stealth
        chrome_options = Options()
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        self.driver = uc.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.wait = WebDriverWait(self.driver, 15)
        logging.info("Ticket snatcher initialized.")

    def load_config(self, config_file):
        """
        Load configuration from JSON file
        """
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # Create default config file
            default_config = {
                "username": "",
                "password": "",
                "target_url": "",
                "ticket_count": 1,
                "price_level": 0,
                "max_retry": 10,
                "retry_interval": 1
            }
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
            logging.warning(f"Created default config file: {config_file}. Please fill in your details.")
            return default_config

    def save_cookies(self, path="cookies.pkl"):
        """Save cookies to file"""
        try:
            with open(path, 'wb') as filehandler:
                pickle.dump(self.driver.get_cookies(), filehandler)
            logging.info("Cookies saved successfully.")
        except Exception as e:
            logging.error(f"Failed to save cookies: {e}")

    def load_cookies(self, path="cookies.pkl"):
        """Load cookies from file"""
        try:
            if os.path.exists(path):
                with open(path, 'rb') as cookiesfile:
                    cookies = pickle.load(cookiesfile)
                    for cookie in cookies:
                        self.driver.add_cookie(cookie)
                logging.info("Cookies loaded successfully.")
                return True
        except Exception as e:
            logging.warning(f"Could not load cookies: {e}")
        return False

    def check_login_status(self):
        """Check if user is already logged in"""
        try:
            # Look for user avatar or profile element
            self.driver.find_element(By.XPATH, "//div[@class='user-info']")
            logging.info("User is already logged in.")
            return True
        except:
            try:
                # Alternative check for login status
                self.driver.find_element(By.XPATH, "//a[contains(@class, 'user-name')]")
                logging.info("User is already logged in.")
                return True
            except:
                logging.info("User is not logged in.")
                return False

    def login(self):
        """Enhanced login process with better error handling"""
        logging.info("Starting login process.")

        # Navigate to Damai homepage
        self.driver.get("https://www.damai.cn/")
        logging.info("Opened Damai homepage.")

        # Try to load existing cookies
        if self.load_cookies():
            self.driver.refresh()
            time.sleep(3)
            if self.check_login_status():
                logging.info("Successfully logged in using saved cookies.")
                return True

        # If not logged in, proceed with manual login
        try:
            # Multiple possible login button selectors
            login_selectors = [
                "//a[text()='登录']",
                "//a[contains(text(), '登录')]",
                "//div[@class='login-btn']",
                "//span[text()='登录']"
            ]

            login_button = None
            for selector in login_selectors:
                try:
                    login_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    break
                except:
                    continue

            if not login_button:
                logging.error("Could not find login button.")
                return False

            logging.info("Found login button.")
            login_button.click()
            logging.info("Clicked login button.")
            time.sleep(2)

        except Exception as e:
            logging.error(f"Failed to click login button: {e}")
            return False

        # Handle login iframe
        try:
            # Wait for iframe to appear
            iframe_selectors = [
                "//iframe[contains(@src, 'login.taobao.com')]",
                "//iframe[contains(@src, 'passport')]",
                "//iframe[@id='alibaba-login-box']"
            ]

            iframe = None
            for selector in iframe_selectors:
                try:
                    iframe = self.wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    break
                except:
                    continue

            if iframe:
                self.driver.switch_to.frame(iframe)
                logging.info("Switched to login iframe.")
            else:
                logging.warning("No login iframe found, proceeding without iframe switch.")

        except Exception as e:
            logging.error(f"Failed to switch to login iframe: {e}")

        # Try to switch to password login if needed
        try:
            time.sleep(2)
            password_login_selectors = [
                "//a[text()='密码登录']",
                "//span[text()='密码登录']",
                "//div[contains(@class, 'password-login')]"
            ]

            for selector in password_login_selectors:
                try:
                    password_login_button = self.driver.find_element(By.XPATH, selector)
                    password_login_button.click()
                    logging.info("Switched to password login.")
                    time.sleep(1)
                    break
                except:
                    continue

        except Exception as e:
            logging.warning(f"Could not switch to password login: {e}")

        # Enter credentials
        try:
            time.sleep(2)
            # Multiple possible input field selectors
            username_selectors = ["fm-login-id", "loginId", "username", "account"]
            password_selectors = ["fm-login-password", "password", "passwd"]

            username_input = None
            password_input = None

            for selector in username_selectors:
                try:
                    username_input = self.driver.find_element(By.ID, selector)
                    break
                except:
                    continue

            for selector in password_selectors:
                try:
                    password_input = self.driver.find_element(By.ID, selector)
                    break
                except:
                    continue

            if username_input and password_input:
                # Clear existing text and enter credentials
                username_input.clear()
                password_input.clear()

                # Simulate human typing
                for char in self.username:
                    username_input.send_keys(char)
                    time.sleep(random.uniform(0.05, 0.15))

                for char in self.password:
                    password_input.send_keys(char)
                    time.sleep(random.uniform(0.05, 0.15))

                logging.info("Entered username and password.")
            else:
                logging.error("Could not find username or password input fields.")
                return False

        except Exception as e:
            logging.error(f"Failed to enter credentials: {e}")
            return False

        # Click login button
        try:
            time.sleep(1)
            login_button_selectors = [
                "//button[text()='登录']",
                "//button[contains(text(), '登录')]",
                "//input[@value='登录']",
                "//a[text()='登录']"
            ]

            login_button = None
            for selector in login_button_selectors:
                try:
                    login_button = self.driver.find_element(By.XPATH, selector)
                    break
                except:
                    continue

            if login_button:
                login_button.click()
                logging.info("Clicked final login button.")
            else:
                logging.error("Could not find final login button.")
                return False

        except Exception as e:
            logging.error(f"Failed to click final login button: {e}")
            return False

        # Wait for login to complete and handle potential verification
        logging.info("Waiting for login to complete...")
        time.sleep(5)

        # Switch back to main content if we were in an iframe
        try:
            self.driver.switch_to.default_content()
        except:
            pass

        # Check if login was successful
        if self.check_login_status():
            self.save_cookies()
            logging.info("Login completed successfully.")
            return True
        else:
            logging.warning("Login may have failed or requires manual verification.")
            return False

    def select_ticket_options(self):
        """Select ticket price level and quantity"""
        try:
            # Wait for page to load
            time.sleep(3)

            # Select price level (票档)
            price_selectors = [
                "//div[contains(@class, 'perform__order__price')]//div[contains(@class, 'price-item')]",
                "//div[contains(@class, 'price-list')]//div[contains(@class, 'price')]",
                "//ul[contains(@class, 'price-list')]//li"
            ]

            price_elements = None
            for selector in price_selectors:
                try:
                    price_elements = self.driver.find_elements(By.XPATH, selector)
                    if price_elements:
                        break
                except:
                    continue

            if price_elements and len(price_elements) > self.price_level:
                target_price = price_elements[self.price_level]
                self.driver.execute_script("arguments[0].click();", target_price)
                logging.info(f"Selected price level {self.price_level}")
                time.sleep(1)
            else:
                logging.warning("Could not find price options, using default.")

            # Select ticket quantity
            quantity_selectors = [
                "//div[contains(@class, 'quantity')]//select",
                "//select[contains(@class, 'quantity')]",
                "//div[contains(@class, 'ticket-count')]//select"
            ]

            for selector in quantity_selectors:
                try:
                    quantity_select = self.driver.find_element(By.XPATH, selector)
                    # Click on the select element
                    quantity_select.click()
                    time.sleep(0.5)

                    # Select the desired quantity
                    option_xpath = f"//option[@value='{self.ticket_count}']"
                    quantity_option = self.driver.find_element(By.XPATH, option_xpath)
                    quantity_option.click()
                    logging.info(f"Selected {self.ticket_count} tickets")
                    time.sleep(1)
                    break
                except:
                    continue

            return True

        except Exception as e:
            logging.error(f"Failed to select ticket options: {e}")
            return False

    def snatch_ticket(self):
        """Enhanced ticket snatching with retry mechanism"""
        logging.info(f"Navigating to target URL: {self.target_url}")

        for attempt in range(self.max_retry):
            try:
                logging.info(f"Attempt {attempt + 1}/{self.max_retry}")

                # Navigate to target page
                self.driver.get(self.target_url)
                time.sleep(2)

                # Check if tickets are available
                if self.check_ticket_availability():
                    logging.info("Tickets are available!")
                else:
                    logging.warning("Tickets may not be available yet.")

                # Select ticket options
                self.select_ticket_options()

                # Find and click buy button with multiple selectors
                buy_button_selectors = [
                    "//div[@class='buy-btn']",
                    "//button[contains(@class, 'buy')]",
                    "//a[contains(@class, 'buy-btn')]",
                    "//div[contains(text(), '立即购买')]",
                    "//button[contains(text(), '立即购买')]",
                    "//div[contains(text(), '立即预订')]",
                    "//button[contains(text(), '立即预订')]"
                ]

                buy_button = None
                for selector in buy_button_selectors:
                    try:
                        buy_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                        break
                    except:
                        continue

                if not buy_button:
                    logging.error("Could not find buy button.")
                    time.sleep(self.retry_interval)
                    continue

                logging.info("Found buy button.")

                # Use JavaScript click for better reliability
                self.driver.execute_script("arguments[0].click();", buy_button)
                logging.info("Clicked buy button.")
                time.sleep(2)

                # Handle potential popup or confirmation
                self.handle_purchase_flow()

                # Check if we successfully proceeded to checkout
                if self.check_checkout_page():
                    logging.info("Successfully proceeded to checkout!")
                    return True
                else:
                    logging.warning("Did not reach checkout page, retrying...")

            except Exception as e:
                logging.error(f"Attempt {attempt + 1} failed: {e}")

            if attempt < self.max_retry - 1:
                logging.info(f"Waiting {self.retry_interval} seconds before retry...")
                time.sleep(self.retry_interval)

        logging.error("All attempts failed.")
        return False

    def check_ticket_availability(self):
        """Check if tickets are currently available for purchase"""
        try:
            # Look for sold out indicators
            sold_out_indicators = [
                "//div[contains(text(), '售罄')]",
                "//div[contains(text(), '暂无票')]",
                "//div[contains(text(), '已售完')]",
                "//span[contains(text(), '售罄')]"
            ]

            for indicator in sold_out_indicators:
                try:
                    element = self.driver.find_element(By.XPATH, indicator)
                    if element.is_displayed():
                        logging.warning("Tickets appear to be sold out.")
                        return False
                except:
                    continue

            # Look for available indicators
            available_indicators = [
                "//div[contains(@class, 'buy-btn')]",
                "//button[contains(text(), '立即购买')]",
                "//button[contains(text(), '立即预订')]"
            ]

            for indicator in available_indicators:
                try:
                    element = self.driver.find_element(By.XPATH, indicator)
                    if element.is_displayed() and element.is_enabled():
                        return True
                except:
                    continue

            return True  # Default to available if we can't determine

        except Exception as e:
            logging.error(f"Error checking ticket availability: {e}")
            return True

    def handle_purchase_flow(self):
        """Handle the purchase flow after clicking buy button"""
        try:
            time.sleep(2)

            # Handle potential confirmation dialogs
            confirm_selectors = [
                "//div[@class='confirm-btn']",
                "//button[contains(text(), '确认')]",
                "//button[contains(text(), '确定')]",
                "//a[contains(text(), '确认')]"
            ]

            for selector in confirm_selectors:
                try:
                    confirm_button = self.driver.find_element(By.XPATH, selector)
                    if confirm_button.is_displayed() and confirm_button.is_enabled():
                        confirm_button.click()
                        logging.info("Clicked confirmation button.")
                        time.sleep(1)
                        break
                except:
                    continue

            # Handle potential captcha or verification
            self.handle_verification()

        except Exception as e:
            logging.error(f"Error in purchase flow: {e}")

    def handle_verification(self):
        """Handle captcha or other verification steps"""
        try:
            # Look for captcha elements
            captcha_selectors = [
                "//div[contains(@class, 'captcha')]",
                "//img[contains(@src, 'captcha')]",
                "//div[contains(@class, 'verification')]"
            ]

            for selector in captcha_selectors:
                try:
                    captcha_element = self.driver.find_element(By.XPATH, selector)
                    if captcha_element.is_displayed():
                        logging.warning("Captcha detected. Manual intervention may be required.")
                        # Give user time to solve captcha manually
                        time.sleep(30)
                        break
                except:
                    continue

        except Exception as e:
            logging.error(f"Error handling verification: {e}")

    def check_checkout_page(self):
        """Check if we successfully reached the checkout page"""
        try:
            checkout_indicators = [
                "//div[contains(@class, 'checkout')]",
                "//div[contains(@class, 'order')]",
                "//div[contains(text(), '订单确认')]",
                "//div[contains(text(), '确认订单')]"
            ]

            for indicator in checkout_indicators:
                try:
                    element = self.driver.find_element(By.XPATH, indicator)
                    if element.is_displayed():
                        return True
                except:
                    continue

            return False

        except Exception as e:
            logging.error(f"Error checking checkout page: {e}")
            return False

    def run(self):
        """Main execution method"""
        logging.info("Starting the ticket snatching process.")
        start_time = datetime.now()

        try:
            # Validate configuration
            if not self.username or not self.password or not self.target_url:
                logging.error("Missing required configuration. Please check config.json file.")
                return False

            # Login process
            if not self.login():
                logging.error("Login failed. Cannot proceed with ticket snatching.")
                return False

            # Wait a bit before starting ticket snatching
            time.sleep(2)

            # Start ticket snatching
            success = self.snatch_ticket()

            if success:
                end_time = datetime.now()
                duration = end_time - start_time
                logging.info(f"Ticket snatching completed successfully in {duration.total_seconds():.2f} seconds!")
                return True
            else:
                logging.error("Ticket snatching failed.")
                return False

        except KeyboardInterrupt:
            logging.info("Process interrupted by user.")
            return False
        except Exception as e:
            logging.error(f"Unexpected error: {e}")
            return False
        finally:
            try:
                self.driver.quit()
                logging.info("Browser closed.")
            except:
                pass

    def __del__(self):
        """Cleanup method"""
        try:
            if hasattr(self, 'driver'):
                self.driver.quit()
        except:
            pass

def main():
    """Main function with command line interface"""
    import argparse

    parser = argparse.ArgumentParser(description='Damai Ticket Snatcher')
    parser.add_argument('--config', default='config.json', help='Configuration file path')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')

    args = parser.parse_args()

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        snatcher = DamaiTicketSnatcher(args.config)
        success = snatcher.run()

        if success:
            print("✅ Ticket snatching completed successfully!")
        else:
            print("❌ Ticket snatching failed.")

    except Exception as e:
        logging.error(f"Failed to initialize ticket snatcher: {e}")
        print("❌ Failed to start ticket snatcher.")

if __name__ == '__main__':
    main()