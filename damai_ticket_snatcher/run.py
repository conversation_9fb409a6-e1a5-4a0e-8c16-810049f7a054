#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的启动脚本
直接运行此文件即可开始抢票
"""

import os
import sys
import json

def check_config():
    """检查配置文件是否存在且已配置"""
    config_file = 'config.json'
    
    if not os.path.exists(config_file):
        print("❌ 配置文件 config.json 不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        required_fields = ['username', 'password', 'target_url']
        missing_fields = []
        
        for field in required_fields:
            if not config.get(field) or config.get(field) == f"your_{field}_here":
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 配置文件中缺少以下必填项: {', '.join(missing_fields)}")
            print("请编辑 config.json 文件并填入正确的信息")
            return False
        
        print("✅ 配置文件检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件格式错误: {e}")
        return False

def main():
    """主函数"""
    print("🎫 大麦网抢票脚本启动中...")
    print("=" * 50)
    
    # 检查配置
    if not check_config():
        print("\n请先配置 config.json 文件后再运行")
        input("按回车键退出...")
        return
    
    # 导入并运行主脚本
    try:
        from main import DamaiTicketSnatcher
        
        print("🚀 开始抢票...")
        snatcher = DamaiTicketSnatcher()
        success = snatcher.run()
        
        if success:
            print("\n🎉 抢票成功！")
        else:
            print("\n😞 抢票失败，请查看日志了解详情")
            
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保已安装所有依赖: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ 运行出错: {e}")
    
    input("\n按回车键退出...")

if __name__ == '__main__':
    main()
