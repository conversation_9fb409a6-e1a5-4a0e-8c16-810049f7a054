# 大麦网抢票脚本

这是一个改进版的大麦网自动抢票脚本，具有更好的稳定性和成功率。

## 功能特点

- ✅ 自动登录（支持Cookie保存）
- ✅ 智能重试机制
- ✅ 多种票档选择
- ✅ 票数量选择
- ✅ 验证码检测
- ✅ 详细日志记录
- ✅ 配置文件管理
- ✅ 反检测机制

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

1. 编辑 `config.json` 文件：

```json
{
    "username": "你的用户名",
    "password": "你的密码", 
    "target_url": "目标票务页面URL",
    "ticket_count": 1,           // 购票数量
    "price_level": 0,            // 票档选择 (0=最高价, 1=第二高价...)
    "max_retry": 10,             // 最大重试次数
    "retry_interval": 1          // 重试间隔(秒)
}
```

2. 配置参数说明：
   - `username`: 大麦网账号用户名
   - `password`: 大麦网账号密码
   - `target_url`: 要抢购的演出详情页URL
   - `ticket_count`: 购买票数（1-6张）
   - `price_level`: 票档选择，0表示最高价位，1表示第二高价位，以此类推
   - `max_retry`: 最大重试次数
   - `retry_interval`: 每次重试间隔时间（秒）

## 使用方法

### 基本使用

```bash
python main.py
```

### 指定配置文件

```bash
python main.py --config my_config.json
```

### 开启调试模式

```bash
python main.py --debug
```

## 使用流程

1. 首次运行会创建默认配置文件 `config.json`
2. 编辑配置文件，填入你的账号信息和目标URL
3. 运行脚本开始抢票
4. 脚本会自动处理登录、选票、购买等流程
5. 如遇验证码会暂停30秒供手动处理

## 注意事项

⚠️ **重要提醒**：
- 请确保账号信息正确
- 建议在开票前几分钟启动脚本
- 遇到验证码时请及时手动处理
- 脚本仅供学习交流，请遵守相关法律法规
- 不保证100%抢票成功，成功率取决于网络、服务器等多种因素

## 日志文件

- 运行日志保存在 `ticket_snatcher.log` 文件中
- 登录状态通过 `cookies.pkl` 文件保存

## 故障排除

1. **登录失败**：
   - 检查用户名密码是否正确
   - 删除 `cookies.pkl` 文件重新登录
   - 检查是否需要手动验证

2. **找不到购买按钮**：
   - 确认目标URL是否正确
   - 检查演出是否已开票
   - 尝试手动访问页面确认元素

3. **脚本卡住**：
   - 检查网络连接
   - 重启脚本
   - 查看日志文件了解详细错误

## 免责声明

本脚本仅供技术学习和研究使用，使用者应当遵守相关法律法规和网站服务条款。作者不对使用本脚本造成的任何后果承担责任。
