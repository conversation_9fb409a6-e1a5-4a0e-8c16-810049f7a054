#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""
import undetected_chromedriver as uc
import time
import ssl
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

def simple_test():
    print("🚀 开始简单测试...")
    
    try:
        print("📱 正在启动Chrome...")
        driver = uc.Chrome()
        print("✅ Chrome启动成功")
        
        print("🌐 正在访问大麦网...")
        driver.get("https://www.damai.cn/")
        print("✅ 页面加载完成")
        
        print("📄 页面标题:", driver.title)
        
        print("⏳ 等待10秒供观察...")
        time.sleep(10)
        
        driver.quit()
        print("🔚 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == '__main__':
    simple_test()
